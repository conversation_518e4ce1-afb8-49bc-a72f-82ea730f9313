package com.shengyu.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.shengyu.business.enums.AuditStatusEnum;
import com.shengyu.common.core.domain.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 供需信息表
 *
 * @TableName t_business_supply_demand
 */
@TableName(value = "t_business_supply_demand")
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "供需信息表")
public class SupplyDemand extends TenantEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    @TableField(value = "user_id")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 信息标题
     */
    @ApiModelProperty("信息标题")
    @TableField(value = "info_title")
    private String infoTitle;

    /**
     * 信息类型（如供求，需求等）
     */
    @ApiModelProperty("信息类型")
    @TableField(value = "info_type")
    private String infoType;

    /**
     * 作物类型（如水稻、小麦等）
     */
    @ApiModelProperty("作物类型")
    @TableField(value = "crop_type")
    private String cropType;

    /**
     * 联系人姓名
     */
    @ApiModelProperty("联系人姓名")
    @TableField(value = "contact_name")
    private String contactName;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    @TableField(value = "phone_number")
    private String phoneNumber;

    /**
     * 身份证号码
     */
    @ApiModelProperty("身份证号码")
    @TableField(value = "id_card")
    private String idCard;

    /**
     * 所属省份
     */
    @ApiModelProperty("所属省份")
    @TableField(value = "province")
    private String province;

    /**
     * 所属城市
     */
    @ApiModelProperty("所属城市")
    @TableField(value = "city")
    private String city;

    /**
     * 所属区县
     */
    @ApiModelProperty("所属区县")
    @TableField(value = "district")
    private String district;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    @TableField(value = "detailed_address")
    private String detailedAddress;

    /**
     * 详细描述
     */
    @ApiModelProperty("详细描述")
    @TableField(value = "description")
    private String description;

    /**
     * 发布时间
     */
    @ApiModelProperty("发布时间")
    @TableField(value = "publish_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /**
     * 审核状态（0待审核 1已通过 2未通过）
     */
    @ApiModelProperty("审核状态")
    @TableField(value = "audit_status")
    private AuditStatusEnum auditStatus;

    /**
     * 审核人ID（关联用户表）
     */
    @ApiModelProperty("审核人ID")
    @TableField(value = "reviewer_id")
    private Long reviewerId;

    /**
     * 审核时间
     */
    @ApiModelProperty("审核时间")
    @TableField(value = "review_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    /**
     * 软删除标识:0-未删除,1-已删除
     */
    @ApiModelProperty("软删除标识:0-未删除,1-已删除")
    @TableField(value = "delete_flag")
    @TableLogic
    private Integer deleteFlag;

}