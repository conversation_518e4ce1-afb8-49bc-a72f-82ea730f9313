package com.shengyu.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shengyu.business.domain.SupplyDemand;
import com.shengyu.business.domain.dto.SupplyDemandDto;
import com.shengyu.business.mapper.BusinessSupplyDemandMapper;
import com.shengyu.business.service.ISupplyDemandService;
import com.shengyu.common.exception.CustomException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_business_supply_demand(供需信息表)】的数据库操作Service实现
 * @createDate 2025-07-16 14:32:12
 */
@Service
public class SupplyDemandServiceImpl extends ServiceImpl<BusinessSupplyDemandMapper, SupplyDemand>
        implements ISupplyDemandService {

    @Override
    public List<SupplyDemandDto> selectList(SupplyDemand supplyDemand) {
        return baseMapper.getList(supplyDemand);
    }

    @Override
    public SupplyDemandDto selectOne(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    @Transactional
    public boolean saveSupplyDemand(SupplyDemand supplyDemand) {
        return super.save(supplyDemand);
    }

    @Override
    @Transactional
    public boolean updateByIdUserId(SupplyDemand supplyDemand, Long userId) {
        if (supplyDemand == null || supplyDemand.getId() == null) {
            throw new CustomException("参数不能为空");
        }
        // 先检查权限，如果通过再更新，避免重复查询
        selectByIdUserId(supplyDemand.getId(), userId);
        return super.updateById(supplyDemand);
    }

    @Override
    @Transactional
    public boolean removeByIdUserId(Long id, Long userId) {
        selectByIdUserId(id, userId);
        return super.removeById(id);
    }

    @Override
    public SupplyDemandDto selectValidById(Long id) {
        return baseMapper.selectValidById(id);
    }

    @Override
    public SupplyDemandDto selectByIdUserId(Long id, Long userId) {
        SupplyDemandDto entity = baseMapper.selectById(id);
        if (null == entity) {
            throw new CustomException("供需信息不存在");
        }
        // 使用Objects.equals避免NPE
        if (!Objects.equals(entity.getUserId(), userId)) {
            throw new CustomException("无权限操作");
        }
        return entity;
    }
}




